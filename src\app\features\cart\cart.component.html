<!-- Cart Page -->
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900">
  <!-- Header -->
  <app-header></app-header>

  <!-- Main Content -->
  <div class="pt-28 pb-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      
      <!-- Page Header -->
      <div class="flex items-center justify-between mb-8">
        <div>
          <h1 class="text-2xl lg:text-3xl font-bold text-white mb-2">
            Корзина
          </h1>
          <p class="text-gray-300">
            {{ cart.total_items }} {{ cart.total_items === 1 ? 'товар' : cart.total_items < 5 ? 'товара' : 'товаров' }}
          </p>
        </div>
        
        <button
          (click)="goToGames()"
          class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          Продолжить покупки
        </button>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="flex justify-center items-center py-20">
        <div class="bg-slate-800/60 backdrop-blur-md border border-purple-400/30 rounded-xl p-6 shadow-2xl">
          <svg class="animate-spin h-10 w-10 text-purple-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="error && !loading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 text-center">
        <p class="text-red-300 mb-4">{{ error }}</p>
        <button 
          (click)="loadCart()" 
          class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          Попробовать снова
        </button>
      </div>

      <!-- Empty Cart -->
      <div *ngIf="!loading && !error && cart.items.length === 0" class="text-center py-20">
        <svg class="w-24 h-24 text-gray-500 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
        </svg>
        <h2 class="text-2xl font-bold text-white mb-4">Корзина пуста</h2>
        <p class="text-gray-400 mb-8">Добавьте игры в корзину, чтобы продолжить</p>
        <button
          (click)="goToGames()"
          class="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all transform hover:scale-105 font-medium shadow-lg"
        >
          Перейти к играм
        </button>
      </div>

      <!-- Cart Items -->
      <div *ngIf="!loading && !error && cart.items.length > 0" class="space-y-6">
        <!-- Cart Items List -->
        <div class="space-y-4">
          <div 
            *ngFor="let item of cart.items" 
            class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6 hover:border-slate-500/60 transition-all"
          >
            <div class="flex flex-col md:flex-row gap-6">
              <!-- Item Image -->
              <div class="w-full md:w-32 h-32 bg-slate-700 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  *ngIf="getItemImage(item)"
                  [src]="getItemImage(item)!"
                  [alt]="getItemTitle(item)"
                  class="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
                  (click)="isGameItem(item) && viewGameDetails(item.game!)"
                >
                <div *ngIf="!getItemImage(item)" class="w-full h-full flex items-center justify-center text-gray-500">
                  <div class="text-center">
                    <svg *ngIf="isGameItem(item)" class="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                    </svg>
                    <div *ngIf="isPackageItem(item)" class="text-4xl mb-2">📦</div>
                    <div class="text-xs">
                      <span *ngIf="isGameItem(item)">Нет изображения</span>
                      <span *ngIf="isPackageItem(item)">Пакет игр</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Item Info -->
              <div class="flex-1 space-y-4">
                <div>
                  <h3
                    class="text-xl font-bold text-white mb-2 cursor-pointer hover:text-blue-400 transition-colors"
                    (click)="isGameItem(item) && viewGameDetails(item.game!)"
                  >
                    {{ getItemTitle(item) }}
                  </h3>
                  <p *ngIf="isGameItem(item) && item.game_obj?.subtitle" class="text-gray-300 text-sm">
                    {{ item.game_obj?.subtitle }}
                  </p>
                  <p class="text-gray-400 text-sm">
                    {{ getItemDescription(item) }}
                  </p>
                  <p class="text-gray-400 text-sm">
                    Добавлено: {{ formatDate(item.added_at) }}
                  </p>
                  <!-- Package games list -->
                  <div *ngIf="isPackageItem(item) && item.game_package_obj?.games?.length" class="mt-2">
                    <div class="text-gray-400 text-sm mb-1">Включает игры:</div>
                    <div class="flex flex-wrap gap-1">
                      <span
                        *ngFor="let game of item.game_package_obj!.games"
                        class="text-xs bg-slate-700 text-gray-300 px-2 py-1 rounded"
                      >
                        {{ game.title }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Price and Actions -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <!-- Price -->
                  <div class="text-right">
                    <div class="space-y-1">
                      <p class="text-lg font-bold text-green-400">
                        {{ formatPrice(getItemPrice(item)) }}
                      </p>
                      <p *ngIf="isPackageItem(item)" class="text-sm text-gray-400">
                        {{ item.game_package_obj?.duration_days }} дней доступа
                      </p>
                    </div>
                  </div>

                  <!-- Remove Button -->
                  <button
                    (click)="removeItem(item)"
                    class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm"
                  >
                    Удалить
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Cart Summary -->
        <div class="bg-slate-800/60 border border-slate-600/50 rounded-lg p-6">
          <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h3 class="text-xl font-bold text-white mb-2">Итого</h3>
              <p class="text-gray-300">
                {{ cart.total_items }} {{ cart.total_items === 1 ? 'товар' : cart.total_items < 5 ? 'товара' : 'товаров' }}
              </p>
            </div>
            
            <div class="text-right">
              <p class="text-3xl font-bold text-green-400 mb-4">
                {{ formatPrice(cart.total_price) }}
              </p>
              
              <div class="flex gap-3">
                <button
                  (click)="clearCart()"
                  class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                  [disabled]="checkoutLoading"
                >
                  Очистить корзину
                </button>
                <button
                  (click)="checkout()"
                  class="px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all transform hover:scale-105 font-medium shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  [disabled]="checkoutLoading || cart.items.length === 0"
                >
                  <span *ngIf="!checkoutLoading">Оформить заказ</span>
                  <span *ngIf="checkoutLoading" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Обработка...
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
