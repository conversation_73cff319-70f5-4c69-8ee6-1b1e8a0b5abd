import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CartService } from '../../../core/services/cart.service';
import { CheckoutService } from '../../../core/services/checkout.service';
import { UserSummaryService } from '../../../core/services/user-summary.service';
import { ModalService } from '../../../core/services/modal.service';
import { Cart, CartItem, Purchase } from '../../../core/models/cart.model';

@Component({
  selector: 'app-profile-cart',
  standalone: false,
  templateUrl: './profile-cart.component.html',
  styleUrl: './profile-cart.component.css'
})
export class ProfileCartComponent implements OnInit, OnDestroy {
  cart: Cart = { items: [], total_items: 0, total_price: 0 };
  loading = false;
  error = '';
  checkoutLoading = false;

  private cartSubscription?: Subscription;

  constructor(
    private cartService: CartService,
    private checkoutService: CheckoutService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCart();
    this.setupCartSubscription();
  }

  ngOnDestroy(): void {
    this.cartSubscription?.unsubscribe();
  }

  private setupCartSubscription(): void {
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cart = cart;
    });
  }

  loadCart(): void {
    this.loading = true;
    this.error = '';

    this.cartService.loadCart().subscribe({
      next: (cart) => {
        this.cart = cart;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить корзину';
        this.loading = false;
      }
    });
  }

  // Helper methods to get item data
  getItemTitle(item: CartItem): string {
    if (item.game_obj) {
      return item.game_obj.title;
    } else if (item.game_package_obj) {
      return item.game_package_obj.name;
    }
    return 'Unknown Item';
  }

  getItemPrice(item: CartItem): string {
    if (item.game_obj) {
      return item.game_obj.price;
    } else if (item.game_package_obj) {
      return item.game_package_obj.price;
    }
    return '0';
  }

  getItemImage(item: CartItem): string | null {
    if (item.game_obj) {
      return item.game_obj.cover_image;
    }
    // Game packages don't have cover images
    return null;
  }

  getItemDescription(item: CartItem): string {
    if (item.game_obj) {
      return item.game_obj.description;
    } else if (item.game_package_obj) {
      return item.game_package_obj.description;
    }
    return '';
  }

  isGameItem(item: CartItem): boolean {
    return !!item.game_obj;
  }

  isPackageItem(item: CartItem): boolean {
    return !!item.game_package_obj;
  }



  removeItem(item: CartItem): void {
    const itemTitle = this.getItemTitle(item);
    this.modalService.confirm(
      'Удалить из корзины',
      `Вы уверены, что хотите удалить "${itemTitle}" из корзины?`
    ).then((confirmed) => {
      if (confirmed) {
        this.cartService.removeFromCart(item.id).subscribe({
          next: () => {
            console.log('Item removed successfully');
          },
          error: (error) => {
            console.error('Error removing item:', error);
            this.modalService.error('Ошибка', 'Не удалось удалить товар: ' + error.message);
          }
        });
      }
    });
  }

  clearCart(): void {
    if (this.cart.items.length === 0) return;

    this.modalService.confirm(
      'Очистить корзину',
      'Вы уверены, что хотите удалить все товары из корзины?'
    ).then((confirmed) => {
      if (confirmed) {
        this.cartService.clearCart().subscribe({
          next: () => {
            console.log('Cart cleared successfully');
          },
          error: (error) => {
            console.error('Error clearing cart:', error);
            this.modalService.error('Ошибка', 'Не удалось очистить корзину: ' + error.message);
          }
        });
      }
    });
  }

  checkout(): void {
    if (this.cart.items.length === 0) return;

    this.checkoutLoading = true;

    this.checkoutService.checkout().subscribe({
      next: (purchases: Purchase[]) => {
        this.checkoutLoading = false;

        // Refresh user summary to update cart count (cart should be cleared after checkout)
        this.userSummaryService.refreshSummary();

        // Show success message
        this.modalService.success(
          'Заказ оформлен',
          `Ваш заказ на сумму ${this.cart.total_price}₽ успешно оформлен! Сейчас вы будете перенаправлены на страницу оплаты.`
        ).then(() => {
          // Navigate directly to purchases section for payment
          this.router.navigate(['/profile/purchases']);
        });
      },
      error: (error) => {
        this.checkoutLoading = false;
        console.error('Checkout error:', error);
        this.modalService.error('Ошибка оформления заказа', error.message || 'Не удалось оформить заказ');
      }
    });
  }

  goToGames(): void {
    this.router.navigate(['/profile'], { fragment: 'catalog' });
  }

  viewGameDetails(gameId: number | undefined): void {
    if (gameId) {
      this.router.navigate(['/games', gameId]);
    }
  }


}
